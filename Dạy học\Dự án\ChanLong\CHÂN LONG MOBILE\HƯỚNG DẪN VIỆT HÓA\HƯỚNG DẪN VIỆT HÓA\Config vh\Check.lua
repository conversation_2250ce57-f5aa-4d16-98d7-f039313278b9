ConfigMgr = ConfigMgr or {}
ConfigMgr["Check_"] = {
    [0] = {
        ["CheckID"] = 0,
        ["SrcFileName"] = "",
        ["InnerID"] = "",
        ["InnerLv"] = "",
        ["DstFileName"] = "",
        ["OuterID"] = "",
        ["OuterLv"] = "",
    },
}

ConfigMgr["Check"] = {
    [1] = {
        ["CheckID"] = 1,
        ["SrcFileName"] = "ActivityBase",
        ["InnerID"] = "DropClientID",
        ["InnerLv"] = "",
        ["DstFileName"] = "DropSubPackClient",
        ["OuterID"] = "SubPackID",
        ["OuterLv"] = "",
    },
    [2] = {
        ["CheckID"] = 2,
        ["SrcFileName"] = "ActivityOnlineActiveReward",
        ["InnerID"] = "DropID",
        ["InnerLv"] = "",
        ["DstFileName"] = "DropPack",
        ["OuterID"] = "DropPackID",
        ["OuterLv"] = "",
    },
    [3] = {
        ["CheckID"] = 3,
        ["SrcFileName"] = "ActivityOnlineActiveReward",
        ["InnerID"] = "DropClientID",
        ["InnerLv"] = "",
        ["DstFileName"] = "DropSubPackClient",
        ["OuterID"] = "SubPackID",
        ["OuterLv"] = "",
    },
    [4] = {
        ["CheckID"] = 4,
        ["SrcFileName"] = "ActivityOnlineLoginReward",
        ["InnerID"] = "DropID",
        ["InnerLv"] = "",
        ["DstFileName"] = "DropPack",
        ["OuterID"] = "DropPackID",
        ["OuterLv"] = "",
    },
    [5] = {
        ["CheckID"] = 5,
        ["SrcFileName"] = "ActivityOnlineLoginReward",
        ["InnerID"] = "DropClientID",
        ["InnerLv"] = "",
        ["DstFileName"] = "DropSubPackClient",
        ["OuterID"] = "SubPackID",
        ["OuterLv"] = "",
    },
    [6] = {
        ["CheckID"] = 6,
        ["SrcFileName"] = "ActivityOnlineRechargeTimeReward",
        ["InnerID"] = "DropID",
        ["InnerLv"] = "",
        ["DstFileName"] = "DropPack",
        ["OuterID"] = "DropPackID",
        ["OuterLv"] = "",
    },
    [7] = {
        ["CheckID"] = 7,
        ["SrcFileName"] = "ActivityOnlineRechargeTimeReward",
        ["InnerID"] = "DropClientID",
        ["InnerLv"] = "",
        ["DstFileName"] = "DropSubPackClient",
        ["OuterID"] = "SubPackID",
        ["OuterLv"] = "",
    },
    [8] = {
        ["CheckID"] = 8,
        ["SrcFileName"] = "ActivityOnlineRechargeValueReward",
        ["InnerID"] = "DropID",
        ["InnerLv"] = "",
        ["DstFileName"] = "DropPack",
        ["OuterID"] = "DropPackID",
        ["OuterLv"] = "",
    },
    [9] = {
        ["CheckID"] = 9,
        ["SrcFileName"] = "ActivityOnlineRechargeValueReward",
        ["InnerID"] = "DropClientID",
        ["InnerLv"] = "",
        ["DstFileName"] = "DropSubPackClient",
        ["OuterID"] = "SubPackID",
        ["OuterLv"] = "",
    },
    [10] = {
        ["CheckID"] = 10,
        ["SrcFileName"] = "ActivityOnlineTimeReward",
        ["InnerID"] = "DropID",
        ["InnerLv"] = "",
        ["DstFileName"] = "DropPack",
        ["OuterID"] = "DropPackID",
        ["OuterLv"] = "",
    },
    [11] = {
        ["CheckID"] = 11,
        ["SrcFileName"] = "ActivityOnlineTimeReward",
        ["InnerID"] = "DropClientID",
        ["InnerLv"] = "",
        ["DstFileName"] = "DropSubPackClient",
        ["OuterID"] = "SubPackID",
        ["OuterLv"] = "",
    },
    [12] = {
        ["CheckID"] = 12,
        ["SrcFileName"] = "ActivityRegister",
        ["InnerID"] = "DropPackID",
        ["InnerLv"] = "",
        ["DstFileName"] = "DropPack",
        ["OuterID"] = "DropPackID",
        ["OuterLv"] = "",
    },
    [13] = {
        ["CheckID"] = 13,
        ["SrcFileName"] = "ActivityRegister",
        ["InnerID"] = "DropPackClientID",
        ["InnerLv"] = "",
        ["DstFileName"] = "DropSubPackClient",
        ["OuterID"] = "SubPackID",
        ["OuterLv"] = "",
    },
    [14] = {
        ["CheckID"] = 14,
        ["SrcFileName"] = "ActivityTurnTable",
        ["InnerID"] = "DropID",
        ["InnerLv"] = "",
        ["DstFileName"] = "DropPack",
        ["OuterID"] = "DropPackID",
        ["OuterLv"] = "",
    },
    [15] = {
        ["CheckID"] = 15,
        ["SrcFileName"] = "ActivityWorldBoss",
        ["InnerID"] = "MonsterType",
        ["InnerLv"] = "",
        ["DstFileName"] = "MonsterBase",
        ["OuterID"] = "ID",
        ["OuterLv"] = "",
    },
    [16] = {
        ["CheckID"] = 16,
        ["SrcFileName"] = "ActivityWorldBoss",
        ["InnerID"] = "EctypeID",
        ["InnerLv"] = "",
        ["DstFileName"] = "ActivityEctype",
        ["OuterID"] = "EctypeID",
        ["OuterLv"] = "",
    },
    [17] = {
        ["CheckID"] = 17,
        ["SrcFileName"] = "ActivityWorldBossReward",
        ["InnerID"] = "DropPackID",
        ["InnerLv"] = "",
        ["DstFileName"] = "DropPack",
        ["OuterID"] = "DropPackID",
        ["OuterLv"] = "",
    },
    [18] = {
        ["CheckID"] = 18,
        ["SrcFileName"] = "ArenaCard",
        ["InnerID"] = "Card1ID",
        ["InnerLv"] = "Card1Star",
        ["DstFileName"] = "CardBase",
        ["OuterID"] = "ID",
        ["OuterLv"] = "StarLevel",
    },
    [19] = {
        ["CheckID"] = 19,
        ["SrcFileName"] = "ArenaCard",
        ["InnerID"] = "Card1EquipGroup",
        ["InnerLv"] = "",
        ["DstFileName"] = "ArenaEquip",
        ["OuterID"] = "CardGroup",
        ["OuterLv"] = "",
    },
    [20] = {
        ["CheckID"] = 20,
        ["SrcFileName"] = "ArenaCard",
        ["InnerID"] = "Card2ID",
        ["InnerLv"] = "Card2Star",
        ["DstFileName"] = "CardBase",
        ["OuterID"] = "ID",
        ["OuterLv"] = "StarLevel",
    },
    [21] = {
        ["CheckID"] = 21,
        ["SrcFileName"] = "ArenaCard",
        ["InnerID"] = "Card2EquipGroup",
        ["InnerLv"] = "",
        ["DstFileName"] = "ArenaEquip",
        ["OuterID"] = "CardGroup",
        ["OuterLv"] = "",
    },
    [22] = {
        ["CheckID"] = 22,
        ["SrcFileName"] = "ArenaCard",
        ["InnerID"] = "Card3ID",
        ["InnerLv"] = "Card3Star",
        ["DstFileName"] = "CardBase",
        ["OuterID"] = "ID",
        ["OuterLv"] = "StarLevel",
    },
    [23] = {
        ["CheckID"] = 23,
        ["SrcFileName"] = "ArenaCard",
        ["InnerID"] = "Card3EquipGroup",
        ["InnerLv"] = "",
        ["DstFileName"] = "ArenaEquip",
        ["OuterID"] = "CardGroup",
        ["OuterLv"] = "",
    },
    [24] = {
        ["CheckID"] = 24,
        ["SrcFileName"] = "ArenaCard",
        ["InnerID"] = "Card4ID",
        ["InnerLv"] = "Card4Star",
        ["DstFileName"] = "CardBase",
        ["OuterID"] = "ID",
        ["OuterLv"] = "StarLevel",
    },
    [25] = {
        ["CheckID"] = 25,
        ["SrcFileName"] = "ArenaCard",
        ["InnerID"] = "Card4EquipGroup",
        ["InnerLv"] = "",
        ["DstFileName"] = "ArenaEquip",
        ["OuterID"] = "CardGroup",
        ["OuterLv"] = "",
    },
    [26] = {
        ["CheckID"] = 26,
        ["SrcFileName"] = "ArenaCard",
        ["InnerID"] = "Card5ID",
        ["InnerLv"] = "Card5Star",
        ["DstFileName"] = "CardBase",
        ["OuterID"] = "ID",
        ["OuterLv"] = "StarLevel",
    },
    [27] = {
        ["CheckID"] = 27,
        ["SrcFileName"] = "ArenaCard",
        ["InnerID"] = "Card5EquipGroup",
        ["InnerLv"] = "",
        ["DstFileName"] = "ArenaEquip",
        ["OuterID"] = "CardGroup",
        ["OuterLv"] = "",
    },
    [28] = {
        ["CheckID"] = 28,
        ["SrcFileName"] = "ArenaEquip",
        ["InnerID"] = "Equip1ID",
        ["InnerLv"] = "Equip1Star",
        ["DstFileName"] = "Equip",
        ["OuterID"] = "ID",
        ["OuterLv"] = "StarLevel",
    },
    [29] = {
        ["CheckID"] = 29,
        ["SrcFileName"] = "ArenaEquip",
        ["InnerID"] = "Equip2ID",
        ["InnerLv"] = "Equip2Star",
        ["DstFileName"] = "Equip",
        ["OuterID"] = "ID",
        ["OuterLv"] = "StarLevel",
    },
    [30] = {
        ["CheckID"] = 30,
        ["SrcFileName"] = "ArenaEquip",
        ["InnerID"] = "Equip3ID",
        ["InnerLv"] = "Equip3Star",
        ["DstFileName"] = "Equip",
        ["OuterID"] = "ID",
        ["OuterLv"] = "StarLevel",
    },
    [31] = {
        ["CheckID"] = 31,
        ["SrcFileName"] = "ArenaEquip",
        ["InnerID"] = "Equip4ID",
        ["InnerLv"] = "Equip4Star",
        ["DstFileName"] = "Equip",
        ["OuterID"] = "ID",
        ["OuterLv"] = "StarLevel",
    },
    [32] = {
        ["CheckID"] = 32,
        ["SrcFileName"] = "ArenaEquip",
        ["InnerID"] = "Equip5ID",
        ["InnerLv"] = "Equip5Star",
        ["DstFileName"] = "Equip",
        ["OuterID"] = "ID",
        ["OuterLv"] = "StarLevel",
    },
    [33] = {
        ["CheckID"] = 33,
        ["SrcFileName"] = "ArenaEquip",
        ["InnerID"] = "Equip6ID",
        ["InnerLv"] = "Equip6Star",
        ["DstFileName"] = "Equip",
        ["OuterID"] = "ID",
        ["OuterLv"] = "StarLevel",
    },
    [34] = {
        ["CheckID"] = 34,
        ["SrcFileName"] = "ArenaPlayer",
        ["InnerID"] = "CardGroup",
        ["InnerLv"] = "",
        ["DstFileName"] = "ArenaCard",
        ["OuterID"] = "CardGroup",
        ["OuterLv"] = "",
    },
    [35] = {
        ["CheckID"] = 35,
        ["SrcFileName"] = "CardBase",
        ["InnerID"] = "CardGroupID1",
        ["InnerLv"] = "",
        ["DstFileName"] = "CardGroup",
        ["OuterID"] = "ID",
        ["OuterLv"] = "",
    },
    [36] = {
        ["CheckID"] = 36,
        ["SrcFileName"] = "CardBase",
        ["InnerID"] = "CardGroupID2",
        ["InnerLv"] = "",
        ["DstFileName"] = "CardGroup",
        ["OuterID"] = "ID",
        ["OuterLv"] = "",
    },
    [37] = {
        ["CheckID"] = 37,
        ["SrcFileName"] = "CardBase",
        ["InnerID"] = "CardGroupID3",
        ["InnerLv"] = "",
        ["DstFileName"] = "CardGroup",
        ["OuterID"] = "ID",
        ["OuterLv"] = "",
    },
    [38] = {
        ["CheckID"] = 38,
        ["SrcFileName"] = "CardBase",
        ["InnerID"] = "CardGroupID4",
        ["InnerLv"] = "",
        ["DstFileName"] = "CardGroup",
        ["OuterID"] = "ID",
        ["OuterLv"] = "",
    },
    [39] = {
        ["CheckID"] = 39,
        ["SrcFileName"] = "CardBase",
        ["InnerID"] = "NormalSkillID",
        ["InnerLv"] = "",
        ["DstFileName"] = "SkillBase",
        ["OuterID"] = "ID",
        ["OuterLv"] = "",
    },
    [40] = {
        ["CheckID"] = 40,
        ["SrcFileName"] = "CardBase",
        ["InnerID"] = "PowerfulSkillID1",
        ["InnerLv"] = "",
        ["DstFileName"] = "SkillBase",
        ["OuterID"] = "ID",
        ["OuterLv"] = "",
    },
    [41] = {
        ["CheckID"] = 41,
        ["SrcFileName"] = "CardBase",
        ["InnerID"] = "PowerfulSkillID2",
        ["InnerLv"] = "",
        ["DstFileName"] = "SkillBase",
        ["OuterID"] = "ID",
        ["OuterLv"] = "",
    },
    [42] = {
        ["CheckID"] = 42,
        ["SrcFileName"] = "CardBase",
        ["InnerID"] = "PowerfulSkillID3",
        ["InnerLv"] = "",
        ["DstFileName"] = "SkillBase",
        ["OuterID"] = "ID",
        ["OuterLv"] = "",
    },
    [43] = {
        ["CheckID"] = 43,
        ["SrcFileName"] = "CardBase",
        ["InnerID"] = "RestrikeSkillID",
        ["InnerLv"] = "",
        ["DstFileName"] = "SkillBase",
        ["OuterID"] = "ID",
        ["OuterLv"] = "",
    },
    [44] = {
        ["CheckID"] = 44,
        ["SrcFileName"] = "CardBase",
        ["InnerID"] = "StarUpHunPoID",
        ["InnerLv"] = "",
        ["DstFileName"] = "CardHunPo",
        ["OuterID"] = "ID",
        ["OuterLv"] = "",
    },
    [45] = {
        ["CheckID"] = 45,
        ["SrcFileName"] = "CardEvoluteSkillCondition",
        ["InnerID"] = "SkillID",
        ["InnerLv"] = "",
        ["DstFileName"] = "SkillBase",
        ["OuterID"] = "ID",
        ["OuterLv"] = "",
    },
    [46] = {
        ["CheckID"] = 46,
        ["SrcFileName"] = "CardDecompose",
        ["InnerID"] = "ID",
        ["InnerLv"] = "",
        ["DstFileName"] = "CardBase",
        ["OuterID"] = "ID",
        ["OuterLv"] = "",
    },
    [47] = {
        ["CheckID"] = 47,
        ["SrcFileName"] = "CardDecompose",
        ["InnerID"] = "ItemID",
        ["InnerLv"] = "",
        ["DstFileName"] = "CardHunPo",
        ["OuterID"] = "ID",
        ["OuterLv"] = "",
    },
    [48] = {
        ["CheckID"] = 48,
        ["SrcFileName"] = "CardEvoluteDanYao",
        ["InnerID"] = "ItemID1",
        ["InnerLv"] = "ItemStarLevel1",
        ["DstFileName"] = "ItemBase",
        ["OuterID"] = "ID",
        ["OuterLv"] = "StarLevel",
    },
    [49] = {
        ["CheckID"] = 49,
        ["SrcFileName"] = "CardEvoluteDanYao",
        ["InnerID"] = "ItemID2",
        ["InnerLv"] = "ItemStarLevel2",
        ["DstFileName"] = "ItemBase",
        ["OuterID"] = "ID",
        ["OuterLv"] = "StarLevel",
    },
    [50] = {
        ["CheckID"] = 50,
        ["SrcFileName"] = "CardEvoluteDanYao",
        ["InnerID"] = "ItemID3",
        ["InnerLv"] = "ItemStarLevel3",
        ["DstFileName"] = "ItemBase",
        ["OuterID"] = "ID",
        ["OuterLv"] = "StarLevel",
    },
    [51] = {
        ["CheckID"] = 51,
        ["SrcFileName"] = "CardHunPo",
        ["InnerID"] = "ID",
        ["InnerLv"] = "CardStarLevel",
        ["DstFileName"] = "CardBase",
        ["OuterID"] = "ID",
        ["OuterLv"] = "StarLevel",
    },
    [52] = {
        ["CheckID"] = 52,
        ["SrcFileName"] = "CardRealmEctype",
        ["InnerID"] = "BattleID1",
        ["InnerLv"] = "",
        ["DstFileName"] = "MapBattle",
        ["OuterID"] = "BattleID",
        ["OuterLv"] = "",
    },
    [53] = {
        ["CheckID"] = 53,
        ["SrcFileName"] = "CardRealmEctype",
        ["InnerID"] = "BattleID2",
        ["InnerLv"] = "",
        ["DstFileName"] = "MapBattle",
        ["OuterID"] = "BattleID",
        ["OuterLv"] = "",
    },
    [54] = {
        ["CheckID"] = 54,
        ["SrcFileName"] = "CardRealmEctype",
        ["InnerID"] = "BattleID3",
        ["InnerLv"] = "",
        ["DstFileName"] = "MapBattle",
        ["OuterID"] = "BattleID",
        ["OuterLv"] = "",
    },
    [55] = {
        ["CheckID"] = 55,
        ["SrcFileName"] = "CardRealmEctype",
        ["InnerID"] = "BattleID4",
        ["InnerLv"] = "",
        ["DstFileName"] = "MapBattle",
        ["OuterID"] = "BattleID",
        ["OuterLv"] = "",
    },
    [56] = {
        ["CheckID"] = 56,
        ["SrcFileName"] = "CardRealmEctype",
        ["InnerID"] = "BattleID5",
        ["InnerLv"] = "",
        ["DstFileName"] = "MapBattle",
        ["OuterID"] = "BattleID",
        ["OuterLv"] = "",
    },
    [57] = {
        ["CheckID"] = 57,
        ["SrcFileName"] = "DropPack",
        ["InnerID"] = "SubPackID",
        ["InnerLv"] = "",
        ["DstFileName"] = "DropSubPack",
        ["OuterID"] = "SubPackID",
        ["OuterLv"] = "",
    },
    [58] = {
        ["CheckID"] = 58,
        ["SrcFileName"] = "DropPackReplace",
        ["InnerID"] = "DropPackID",
        ["InnerLv"] = "",
        ["DstFileName"] = "DropPack",
        ["OuterID"] = "DropPackID",
        ["OuterLv"] = "",
    },
    [59] = {
        ["CheckID"] = 59,
        ["SrcFileName"] = "DropPackReplace",
        ["InnerID"] = "ReviseDrop",
        ["InnerLv"] = "",
        ["DstFileName"] = "DropPack",
        ["OuterID"] = "DropPackID",
        ["OuterLv"] = "",
    },
    [60] = {
        ["CheckID"] = 60,
        ["SrcFileName"] = "Equip",
        ["InnerID"] = "HeChengFormulaID",
        ["InnerLv"] = "HeChengFormulaStar",
        ["DstFileName"] = "ItemBase",
        ["OuterID"] = "ID",
        ["OuterLv"] = "StarLevel",
    },
    [61] = {
        ["CheckID"] = 61,
        ["SrcFileName"] = "Equip",
        ["InnerID"] = "HeChengMaterialGroupID",
        ["InnerLv"] = "",
        ["DstFileName"] = "EquipHeChengMaterial",
        ["OuterID"] = "GroupID",
        ["OuterLv"] = "",
    },
    [62] = {
        ["CheckID"] = 62,
        ["SrcFileName"] = "Equip",
        ["InnerID"] = "ChongZhuMaterialGroupID",
        ["InnerLv"] = "",
        ["DstFileName"] = "EquipWorkMaterialGroup",
        ["OuterID"] = "GroupID",
        ["OuterLv"] = "",
    },
    [63] = {
        ["CheckID"] = 63,
        ["SrcFileName"] = "EquipHeChengMaterial",
        ["InnerID"] = "MaterialID1",
        ["InnerLv"] = "MaterialStarLevel1",
        ["DstFileName"] = "ItemBase",
        ["OuterID"] = "ID",
        ["OuterLv"] = "StarLevel",
    },
    [64] = {
        ["CheckID"] = 64,
        ["SrcFileName"] = "EquipHeChengMaterial",
        ["InnerID"] = "MaterialID2",
        ["InnerLv"] = "MaterialStarLevel2",
        ["DstFileName"] = "ItemBase",
        ["OuterID"] = "ID",
        ["OuterLv"] = "StarLevel",
    },
    [65] = {
        ["CheckID"] = 65,
        ["SrcFileName"] = "EquipHeChengMaterial",
        ["InnerID"] = "MaterialID3",
        ["InnerLv"] = "MaterialStarLevel3",
        ["DstFileName"] = "ItemBase",
        ["OuterID"] = "ID",
        ["OuterLv"] = "StarLevel",
    },
    [66] = {
        ["CheckID"] = 66,
        ["SrcFileName"] = "EquipHeChengMaterial",
        ["InnerID"] = "MaterialID4",
        ["InnerLv"] = "MaterialStarLevel4",
        ["DstFileName"] = "ItemBase",
        ["OuterID"] = "ID",
        ["OuterLv"] = "StarLevel",
    },
    [67] = {
        ["CheckID"] = 67,
        ["SrcFileName"] = "EquipWorkMaterialGroup",
        ["InnerID"] = "MaterialID1",
        ["InnerLv"] = "MaterialStarLevel1",
        ["DstFileName"] = "ItemBase",
        ["OuterID"] = "ID",
        ["OuterLv"] = "StarLevel",
    },
    [68] = {
        ["CheckID"] = 68,
        ["SrcFileName"] = "EquipWorkMaterialGroup",
        ["InnerID"] = "MaterialID2",
        ["InnerLv"] = "MaterialStarLevel2",
        ["DstFileName"] = "ItemBase",
        ["OuterID"] = "ID",
        ["OuterLv"] = "StarLevel",
    },
    [69] = {
        ["CheckID"] = 69,
        ["SrcFileName"] = "EquipWorkMaterialGroup",
        ["InnerID"] = "MaterialID3",
        ["InnerLv"] = "MaterialStarLevel3",
        ["DstFileName"] = "ItemBase",
        ["OuterID"] = "ID",
        ["OuterLv"] = "StarLevel",
    },
    [70] = {
        ["CheckID"] = 70,
        ["SrcFileName"] = "EquipWorkMaterialGroup",
        ["InnerID"] = "MaterialID4",
        ["InnerLv"] = "MaterialStarLevel4",
        ["DstFileName"] = "ItemBase",
        ["OuterID"] = "ID",
        ["OuterLv"] = "StarLevel",
    },
    [71] = {
        ["CheckID"] = 71,
        ["SrcFileName"] = "EquipWorkMaterialGroup",
        ["InnerID"] = "MaterialID5",
        ["InnerLv"] = "MaterialStarLevel5",
        ["DstFileName"] = "ItemBase",
        ["OuterID"] = "ID",
        ["OuterLv"] = "StarLevel",
    },
    [72] = {
        ["CheckID"] = 72,
        ["SrcFileName"] = "EquipWorkMaterialGroup",
        ["InnerID"] = "MaterialID6",
        ["InnerLv"] = "MaterialStarLevel6",
        ["DstFileName"] = "ItemBase",
        ["OuterID"] = "ID",
        ["OuterLv"] = "StarLevel",
    },
    [73] = {
        ["CheckID"] = 73,
        ["SrcFileName"] = "FunctionOpenLevel",
        ["InnerID"] = "EndGuideID",
        ["InnerLv"] = "",
        ["DstFileName"] = "Guide",
        ["OuterID"] = "GuideID",
        ["OuterLv"] = "",
    },
    [74] = {
        ["CheckID"] = 74,
        ["SrcFileName"] = "ItemBase",
        ["InnerID"] = "DropID",
        ["InnerLv"] = "",
        ["DstFileName"] = "DropPack",
        ["OuterID"] = "DropPackID",
        ["OuterLv"] = "",
    },
    [75] = {
        ["CheckID"] = 75,
        ["SrcFileName"] = "ItemCompose",
        ["InnerID"] = "TargetID",
        ["InnerLv"] = "TargetStarLevel",
        ["DstFileName"] = "ItemBase",
        ["OuterID"] = "ID",
        ["OuterLv"] = "StarLevel",
    },
    [76] = {
        ["CheckID"] = 76,
        ["SrcFileName"] = "ItemCompose",
        ["InnerID"] = "MaterialID1",
        ["InnerLv"] = "MaterialStarLevel1",
        ["DstFileName"] = "ItemBase",
        ["OuterID"] = "ID",
        ["OuterLv"] = "StarLevel",
    },
    [77] = {
        ["CheckID"] = 77,
        ["SrcFileName"] = "ItemCompose",
        ["InnerID"] = "MaterialID2",
        ["InnerLv"] = "MaterialStarLevel2",
        ["DstFileName"] = "ItemBase",
        ["OuterID"] = "ID",
        ["OuterLv"] = "StarLevel",
    },
    [78] = {
        ["CheckID"] = 78,
        ["SrcFileName"] = "ItemCompose",
        ["InnerID"] = "MaterialID3",
        ["InnerLv"] = "MaterialStarLevel3",
        ["DstFileName"] = "ItemBase",
        ["OuterID"] = "ID",
        ["OuterLv"] = "StarLevel",
    },
    [79] = {
        ["CheckID"] = 79,
        ["SrcFileName"] = "MailBox",
        ["InnerID"] = "DropServerID",
        ["InnerLv"] = "",
        ["DstFileName"] = "DropPack",
        ["OuterID"] = "DropPackID",
        ["OuterLv"] = "",
    },
    [80] = {
        ["CheckID"] = 80,
        ["SrcFileName"] = "MapEctype",
        ["InnerID"] = "MapID",
        ["InnerLv"] = "",
        ["DstFileName"] = "MapBase",
        ["OuterID"] = "ID",
        ["OuterLv"] = "",
    },
    [81] = {
        ["CheckID"] = 81,
        ["SrcFileName"] = "MapEctype",
        ["InnerID"] = "SubEctype1",
        ["InnerLv"] = "",
        ["DstFileName"] = "MapEctypeSub",
        ["OuterID"] = "SubEctypeID",
        ["OuterLv"] = "",
    },
    [82] = {
        ["CheckID"] = 82,
        ["SrcFileName"] = "MapEctype",
        ["InnerID"] = "SubEctype2",
        ["InnerLv"] = "",
        ["DstFileName"] = "MapEctypeSub",
        ["OuterID"] = "SubEctypeID",
        ["OuterLv"] = "",
    },
    [83] = {
        ["CheckID"] = 83,
        ["SrcFileName"] = "MapEctype",
        ["InnerID"] = "SubEctype3",
        ["InnerLv"] = "",
        ["DstFileName"] = "MapEctypeSub",
        ["OuterID"] = "SubEctypeID",
        ["OuterLv"] = "",
    },
    [84] = {
        ["CheckID"] = 84,
        ["SrcFileName"] = "MapEctype",
        ["InnerID"] = "ShowDropPackID1",
        ["InnerLv"] = "",
        ["DstFileName"] = "DropSubPackClient",
        ["OuterID"] = "SubPackID",
        ["OuterLv"] = "",
    },
    [85] = {
        ["CheckID"] = 85,
        ["SrcFileName"] = "MapEctype",
        ["InnerID"] = "ShowDropPackID2",
        ["InnerLv"] = "",
        ["DstFileName"] = "DropSubPackClient",
        ["OuterID"] = "SubPackID",
        ["OuterLv"] = "",
    },
    [86] = {
        ["CheckID"] = 86,
        ["SrcFileName"] = "MapEctype",
        ["InnerID"] = "ShowDropPackID3",
        ["InnerLv"] = "",
        ["DstFileName"] = "DropSubPackClient",
        ["OuterID"] = "SubPackID",
        ["OuterLv"] = "",
    },
    [87] = {
        ["CheckID"] = 87,
        ["SrcFileName"] = "MapEctype",
        ["InnerID"] = "ShowDropPackID4",
        ["InnerLv"] = "",
        ["DstFileName"] = "DropSubPackClient",
        ["OuterID"] = "SubPackID",
        ["OuterLv"] = "",
    },
    [88] = {
        ["CheckID"] = 88,
        ["SrcFileName"] = "MapEctype",
        ["InnerID"] = "ShowDropPackID5",
        ["InnerLv"] = "",
        ["DstFileName"] = "DropSubPackClient",
        ["OuterID"] = "SubPackID",
        ["OuterLv"] = "",
    },
    [89] = {
        ["CheckID"] = 89,
        ["SrcFileName"] = "MapEctype",
        ["InnerID"] = "ShowDropPackID6",
        ["InnerLv"] = "",
        ["DstFileName"] = "DropSubPackClient",
        ["OuterID"] = "SubPackID",
        ["OuterLv"] = "",
    },
    [90] = {
        ["CheckID"] = 90,
        ["SrcFileName"] = "MapEctypeJingYing",
        ["InnerID"] = "MapID",
        ["InnerLv"] = "",
        ["DstFileName"] = "MapBase",
        ["OuterID"] = "ID",
        ["OuterLv"] = "",
    },
    [91] = {
        ["CheckID"] = 91,
        ["SrcFileName"] = "MapEctypeJingYing",
        ["InnerID"] = "BattleID1",
        ["InnerLv"] = "",
        ["DstFileName"] = "MapBattle",
        ["OuterID"] = "BattleID",
        ["OuterLv"] = "",
    },
    [92] = {
        ["CheckID"] = 92,
        ["SrcFileName"] = "MapEctypeJingYing",
        ["InnerID"] = "BattleID2",
        ["InnerLv"] = "",
        ["DstFileName"] = "MapBattle",
        ["OuterID"] = "BattleID",
        ["OuterLv"] = "",
    },
    [93] = {
        ["CheckID"] = 93,
        ["SrcFileName"] = "MapEctypeJingYing",
        ["InnerID"] = "BattleID3",
        ["InnerLv"] = "",
        ["DstFileName"] = "MapBattle",
        ["OuterID"] = "BattleID",
        ["OuterLv"] = "",
    },
    [94] = {
        ["CheckID"] = 94,
        ["SrcFileName"] = "MapEctypeJingYing",
        ["InnerID"] = "BattleID4",
        ["InnerLv"] = "",
        ["DstFileName"] = "MapBattle",
        ["OuterID"] = "BattleID",
        ["OuterLv"] = "",
    },
    [95] = {
        ["CheckID"] = 95,
        ["SrcFileName"] = "MapEctypeJingYing",
        ["InnerID"] = "BattleID5",
        ["InnerLv"] = "",
        ["DstFileName"] = "MapBattle",
        ["OuterID"] = "BattleID",
        ["OuterLv"] = "",
    },
    [96] = {
        ["CheckID"] = 96,
        ["SrcFileName"] = "MapEctypeJingYing",
        ["InnerID"] = "ShowDropPackID1",
        ["InnerLv"] = "",
        ["DstFileName"] = "DropSubPackClient",
        ["OuterID"] = "SubPackID",
        ["OuterLv"] = "",
    },
    [97] = {
        ["CheckID"] = 97,
        ["SrcFileName"] = "MapEctypeJingYing",
        ["InnerID"] = "ShowDropPackID2",
        ["InnerLv"] = "",
        ["DstFileName"] = "DropSubPackClient",
        ["OuterID"] = "SubPackID",
        ["OuterLv"] = "",
    },
    [98] = {
        ["CheckID"] = 98,
        ["SrcFileName"] = "MapEctypeJingYing",
        ["InnerID"] = "ShowDropPackID3",
        ["InnerLv"] = "",
        ["DstFileName"] = "DropSubPackClient",
        ["OuterID"] = "SubPackID",
        ["OuterLv"] = "",
    },
    [99] = {
        ["CheckID"] = 99,
        ["SrcFileName"] = "MapEctypeJingYing",
        ["InnerID"] = "ShowDropPackID4",
        ["InnerLv"] = "",
        ["DstFileName"] = "DropSubPackClient",
        ["OuterID"] = "SubPackID",
        ["OuterLv"] = "",
    },
    [100] = {
        ["CheckID"] = 100,
        ["SrcFileName"] = "MapEctypeJingYing",
        ["InnerID"] = "ShowDropPackID5",
        ["InnerLv"] = "",
        ["DstFileName"] = "DropSubPackClient",
        ["OuterID"] = "SubPackID",
        ["OuterLv"] = "",
    },
    [101] = {
        ["CheckID"] = 101,
        ["SrcFileName"] = "MapEctypeJingYing",
        ["InnerID"] = "ShowDropPackID6",
        ["InnerLv"] = "",
        ["DstFileName"] = "DropSubPackClient",
        ["OuterID"] = "SubPackID",
        ["OuterLv"] = "",
    },
    [102] = {
        ["CheckID"] = 102,
        ["SrcFileName"] = "MapEctypeSub",
        ["InnerID"] = "EctypeID",
        ["InnerLv"] = "",
        ["DstFileName"] = "MapEctype",
        ["OuterID"] = "EctypeID",
        ["OuterLv"] = "",
    },
    [103] = {
        ["CheckID"] = 103,
        ["SrcFileName"] = "MapEctypeSub",
        ["InnerID"] = "BattleID1",
        ["InnerLv"] = "",
        ["DstFileName"] = "MapBattle",
        ["OuterID"] = "BattleID",
        ["OuterLv"] = "",
    },
    [104] = {
        ["CheckID"] = 104,
        ["SrcFileName"] = "MapEctypeSub",
        ["InnerID"] = "BattleID2",
        ["InnerLv"] = "",
        ["DstFileName"] = "MapBattle",
        ["OuterID"] = "BattleID",
        ["OuterLv"] = "",
    },
    [105] = {
        ["CheckID"] = 105,
        ["SrcFileName"] = "MapEctypeSub",
        ["InnerID"] = "BattleID3",
        ["InnerLv"] = "",
        ["DstFileName"] = "MapBattle",
        ["OuterID"] = "BattleID",
        ["OuterLv"] = "",
    },
    [106] = {
        ["CheckID"] = 106,
        ["SrcFileName"] = "MapEctypeSub",
        ["InnerID"] = "BattleID4",
        ["InnerLv"] = "",
        ["DstFileName"] = "MapBattle",
        ["OuterID"] = "BattleID",
        ["OuterLv"] = "",
    },
    [107] = {
        ["CheckID"] = 107,
        ["SrcFileName"] = "MapEctypeSub",
        ["InnerID"] = "BattleID5",
        ["InnerLv"] = "",
        ["DstFileName"] = "MapBattle",
        ["OuterID"] = "BattleID",
        ["OuterLv"] = "",
    },
    [108] = {
        ["CheckID"] = 108,
        ["SrcFileName"] = "MapEctypeSub",
        ["InnerID"] = "HelperID",
        ["InnerLv"] = "",
        ["DstFileName"] = "MonsterBase",
        ["OuterID"] = "ID",
        ["OuterLv"] = "",
    },
    [109] = {
        ["CheckID"] = 109,
        ["SrcFileName"] = "MapBattle",
        ["InnerID"] = "WinRewardPack",
        ["InnerLv"] = "",
        ["DstFileName"] = "DropPack",
        ["OuterID"] = "DropPackID",
        ["OuterLv"] = "",
    },
    [110] = {
        ["CheckID"] = 110,
        ["SrcFileName"] = "MapBattle",
        ["InnerID"] = "MonsterID1",
        ["InnerLv"] = "",
        ["DstFileName"] = "MapBattleMonsterBorn",
        ["OuterID"] = "MonsterID",
        ["OuterLv"] = "",
    },
    [111] = {
        ["CheckID"] = 111,
        ["SrcFileName"] = "MapBattle",
        ["InnerID"] = "MonsterID2",
        ["InnerLv"] = "",
        ["DstFileName"] = "MapBattleMonsterBorn",
        ["OuterID"] = "MonsterID",
        ["OuterLv"] = "",
    },
    [112] = {
        ["CheckID"] = 112,
        ["SrcFileName"] = "MapBattle",
        ["InnerID"] = "MonsterID3",
        ["InnerLv"] = "",
        ["DstFileName"] = "MapBattleMonsterBorn",
        ["OuterID"] = "MonsterID",
        ["OuterLv"] = "",
    },
    [113] = {
        ["CheckID"] = 113,
        ["SrcFileName"] = "MapBattle",
        ["InnerID"] = "MonsterID4",
        ["InnerLv"] = "",
        ["DstFileName"] = "MapBattleMonsterBorn",
        ["OuterID"] = "MonsterID",
        ["OuterLv"] = "",
    },
    [114] = {
        ["CheckID"] = 114,
        ["SrcFileName"] = "MapBattle",
        ["InnerID"] = "MonsterID5",
        ["InnerLv"] = "",
        ["DstFileName"] = "MapBattleMonsterBorn",
        ["OuterID"] = "MonsterID",
        ["OuterLv"] = "",
    },
    [115] = {
        ["CheckID"] = 115,
        ["SrcFileName"] = "MapBattle",
        ["InnerID"] = "MonsterID6",
        ["InnerLv"] = "",
        ["DstFileName"] = "MapBattleMonsterBorn",
        ["OuterID"] = "MonsterID",
        ["OuterLv"] = "",
    },
    [116] = {
        ["CheckID"] = 116,
        ["SrcFileName"] = "MapBattle",
        ["InnerID"] = "SubMonsterID1",
        ["InnerLv"] = "",
        ["DstFileName"] = "MapBattleMonsterBorn",
        ["OuterID"] = "MonsterID",
        ["OuterLv"] = "",
    },
    [117] = {
        ["CheckID"] = 117,
        ["SrcFileName"] = "MapBattle",
        ["InnerID"] = "SubMonsterID2",
        ["InnerLv"] = "",
        ["DstFileName"] = "MapBattleMonsterBorn",
        ["OuterID"] = "MonsterID",
        ["OuterLv"] = "",
    },
    [118] = {
        ["CheckID"] = 118,
        ["SrcFileName"] = "MapBattle",
        ["InnerID"] = "SubMonsterID3",
        ["InnerLv"] = "",
        ["DstFileName"] = "MapBattleMonsterBorn",
        ["OuterID"] = "MonsterID",
        ["OuterLv"] = "",
    },
    [119] = {
        ["CheckID"] = 119,
        ["SrcFileName"] = "MapBattle",
        ["InnerID"] = "HelperID",
        ["InnerLv"] = "",
        ["DstFileName"] = "MonsterBase",
        ["OuterID"] = "ID",
        ["OuterLv"] = "",
    },
    [120] = {
        ["CheckID"] = 120,
        ["SrcFileName"] = "MapBattleMonsterBorn",
        ["InnerID"] = "MonsterType",
        ["InnerLv"] = "",
        ["DstFileName"] = "MonsterBase",
        ["OuterID"] = "ID",
        ["OuterLv"] = "",
    },
    [121] = {
        ["CheckID"] = 121,
        ["SrcFileName"] = "MapBattleMonsterBorn",
        ["InnerID"] = "DropPackID",
        ["InnerLv"] = "",
        ["DstFileName"] = "DropPack",
        ["OuterID"] = "DropPackID",
        ["OuterLv"] = "",
    },
    [122] = {
        ["CheckID"] = 122,
        ["SrcFileName"] = "MarketSummon",
        ["InnerID"] = "DropPackID",
        ["InnerLv"] = "",
        ["DstFileName"] = "DropPack",
        ["OuterID"] = "DropPackID",
        ["OuterLv"] = "",
    },
    [123] = {
        ["CheckID"] = 123,
        ["SrcFileName"] = "MarketSummon",
        ["InnerID"] = "DropPackReplaceID",
        ["InnerLv"] = "",
        ["DstFileName"] = "DropPack",
        ["OuterID"] = "DropPackID",
        ["OuterLv"] = "",
    },
    [124] = {
        ["CheckID"] = 124,
        ["SrcFileName"] = "MonsterBase",
        ["InnerID"] = "NormalSkillID",
        ["InnerLv"] = "",
        ["DstFileName"] = "SkillBase",
        ["OuterID"] = "ID",
        ["OuterLv"] = "",
    },
    [125] = {
        ["CheckID"] = 125,
        ["SrcFileName"] = "MonsterBase",
        ["InnerID"] = "PowerfulSkillID1",
        ["InnerLv"] = "",
        ["DstFileName"] = "SkillBase",
        ["OuterID"] = "ID",
        ["OuterLv"] = "",
    },
    [126] = {
        ["CheckID"] = 126,
        ["SrcFileName"] = "MonsterBase",
        ["InnerID"] = "PowerfulSkillID2",
        ["InnerLv"] = "",
        ["DstFileName"] = "SkillBase",
        ["OuterID"] = "ID",
        ["OuterLv"] = "",
    },
    [127] = {
        ["CheckID"] = 127,
        ["SrcFileName"] = "MonsterBase",
        ["InnerID"] = "PowerfulSkillID3",
        ["InnerLv"] = "",
        ["DstFileName"] = "SkillBase",
        ["OuterID"] = "ID",
        ["OuterLv"] = "",
    },
    [128] = {
        ["CheckID"] = 128,
        ["SrcFileName"] = "MonsterBase",
        ["InnerID"] = "RestrikeSkillID",
        ["InnerLv"] = "",
        ["DstFileName"] = "SkillBase",
        ["OuterID"] = "ID",
        ["OuterLv"] = "",
    },
    [129] = {
        ["CheckID"] = 129,
        ["SrcFileName"] = "PlayerExp",
        ["InnerID"] = "DropID",
        ["InnerLv"] = "",
        ["DstFileName"] = "DropPack",
        ["OuterID"] = "DropPackID",
        ["OuterLv"] = "",
    },
    [130] = {
        ["CheckID"] = 130,
        ["SrcFileName"] = "PlayerCreate",
        ["InnerID"] = "CardID",
        ["InnerLv"] = "StarLevel",
        ["DstFileName"] = "CardBase",
        ["OuterID"] = "ID",
        ["OuterLv"] = "StarLevel",
    },
    [131] = {
        ["CheckID"] = 131,
        ["SrcFileName"] = "PlayerCreate",
        ["InnerID"] = "BaseCard1",
        ["InnerLv"] = "CardStar1",
        ["DstFileName"] = "CardBase",
        ["OuterID"] = "ID",
        ["OuterLv"] = "StarLevel",
    },
    [132] = {
        ["CheckID"] = 132,
        ["SrcFileName"] = "PlayerCreate",
        ["InnerID"] = "BaseCard2",
        ["InnerLv"] = "CardStar2",
        ["DstFileName"] = "CardBase",
        ["OuterID"] = "ID",
        ["OuterLv"] = "StarLevel",
    },
    [133] = {
        ["CheckID"] = 133,
        ["SrcFileName"] = "PlayerCreate",
        ["InnerID"] = "BaseCard3",
        ["InnerLv"] = "CardStar3",
        ["DstFileName"] = "CardBase",
        ["OuterID"] = "ID",
        ["OuterLv"] = "StarLevel",
    },
    [134] = {
        ["CheckID"] = 134,
        ["SrcFileName"] = "PlayerCreate",
        ["InnerID"] = "BaseCard4",
        ["InnerLv"] = "CardStar4",
        ["DstFileName"] = "CardBase",
        ["OuterID"] = "ID",
        ["OuterLv"] = "StarLevel",
    },
    [135] = {
        ["CheckID"] = 135,
        ["SrcFileName"] = "PlayerCreate",
        ["InnerID"] = "BaseCard5",
        ["InnerLv"] = "CardStar5",
        ["DstFileName"] = "CardBase",
        ["OuterID"] = "ID",
        ["OuterLv"] = "StarLevel",
    },
    [136] = {
        ["CheckID"] = 136,
        ["SrcFileName"] = "PlayerCreate",
        ["InnerID"] = "BaseCard6",
        ["InnerLv"] = "CardStar6",
        ["DstFileName"] = "CardBase",
        ["OuterID"] = "ID",
        ["OuterLv"] = "StarLevel",
    },
    [137] = {
        ["CheckID"] = 137,
        ["SrcFileName"] = "RandEctype",
        ["InnerID"] = "ActivityEctypeID",
        ["InnerLv"] = "",
        ["DstFileName"] = "ActivityEctype",
        ["OuterID"] = "EctypeID",
        ["OuterLv"] = "",
    },
    [138] = {
        ["CheckID"] = 138,
        ["SrcFileName"] = "RandEctypeBattleGroup",
        ["InnerID"] = "RandEctypeBuzhenGroupID",
        ["InnerLv"] = "",
        ["DstFileName"] = "RandEctypeBuZhenGroup",
        ["OuterID"] = "RandBuZhenID",
        ["OuterLv"] = "",
    },
    [139] = {
        ["CheckID"] = 139,
        ["SrcFileName"] = "RandEctypeBattleGroup",
        ["InnerID"] = "RandMonsterGroupID1",
        ["InnerLv"] = "",
        ["DstFileName"] = "RandEctypeMonsterGroup",
        ["OuterID"] = "RandMonsterGroupID",
        ["OuterLv"] = "",
    },
    [140] = {
        ["CheckID"] = 140,
        ["SrcFileName"] = "RandEctypeBattleGroup",
        ["InnerID"] = "RandMonsterGroupID2",
        ["InnerLv"] = "",
        ["DstFileName"] = "RandEctypeMonsterGroup",
        ["OuterID"] = "RandMonsterGroupID",
        ["OuterLv"] = "",
    },
    [141] = {
        ["CheckID"] = 141,
        ["SrcFileName"] = "RandEctypeBattleGroup",
        ["InnerID"] = "RandMonsterGroupID3",
        ["InnerLv"] = "",
        ["DstFileName"] = "RandEctypeMonsterGroup",
        ["OuterID"] = "RandMonsterGroupID",
        ["OuterLv"] = "",
    },
    [142] = {
        ["CheckID"] = 142,
        ["SrcFileName"] = "RandEctypeBattleGroup",
        ["InnerID"] = "RandMonsterGroupID4",
        ["InnerLv"] = "",
        ["DstFileName"] = "RandEctypeMonsterGroup",
        ["OuterID"] = "RandMonsterGroupID",
        ["OuterLv"] = "",
    },
    [143] = {
        ["CheckID"] = 143,
        ["SrcFileName"] = "RandEctypeBattleGroup",
        ["InnerID"] = "RandMonsterGroupID5",
        ["InnerLv"] = "",
        ["DstFileName"] = "RandEctypeMonsterGroup",
        ["OuterID"] = "RandMonsterGroupID",
        ["OuterLv"] = "",
    },
    [144] = {
        ["CheckID"] = 144,
        ["SrcFileName"] = "RandEctypeBattleGroup",
        ["InnerID"] = "RandMonsterGroupID6",
        ["InnerLv"] = "",
        ["DstFileName"] = "RandEctypeMonsterGroup",
        ["OuterID"] = "RandMonsterGroupID",
        ["OuterLv"] = "",
    },
    [145] = {
        ["CheckID"] = 145,
        ["SrcFileName"] = "RandEctypeBattleGroup",
        ["InnerID"] = "SubRandMonsterGroupID1",
        ["InnerLv"] = "",
        ["DstFileName"] = "RandEctypeMonsterGroup",
        ["OuterID"] = "RandMonsterGroupID",
        ["OuterLv"] = "",
    },
    [146] = {
        ["CheckID"] = 146,
        ["SrcFileName"] = "RandEctypeBattleGroup",
        ["InnerID"] = "SubRandMonsterGroupID2",
        ["InnerLv"] = "",
        ["DstFileName"] = "RandEctypeMonsterGroup",
        ["OuterID"] = "RandMonsterGroupID",
        ["OuterLv"] = "",
    },
    [147] = {
        ["CheckID"] = 147,
        ["SrcFileName"] = "RandEctypeBattleGroup",
        ["InnerID"] = "SubRandMonsterGroupID3",
        ["InnerLv"] = "",
        ["DstFileName"] = "RandEctypeMonsterGroup",
        ["OuterID"] = "RandMonsterGroupID",
        ["OuterLv"] = "",
    },
    [148] = {
        ["CheckID"] = 148,
        ["SrcFileName"] = "RandEctypeBattleGroup",
        ["InnerID"] = "WinRewardPack",
        ["InnerLv"] = "",
        ["DstFileName"] = "DropPack",
        ["OuterID"] = "DropPackID",
        ["OuterLv"] = "",
    },
    [149] = {
        ["CheckID"] = 149,
        ["SrcFileName"] = "RandEctypeMonsterGroup",
        ["InnerID"] = "MonsterBaseID",
        ["InnerLv"] = "",
        ["DstFileName"] = "MonsterBase",
        ["OuterID"] = "ID",
        ["OuterLv"] = "",
    },
    [150] = {
        ["CheckID"] = 150,
        ["SrcFileName"] = "RandEctypeMonsterGroup",
        ["InnerID"] = "DropPackID",
        ["InnerLv"] = "",
        ["DstFileName"] = "DropPack",
        ["OuterID"] = "DropPackID",
        ["OuterLv"] = "",
    },
    [151] = {
        ["CheckID"] = 151,
        ["SrcFileName"] = "ShopItem",
        ["InnerID"] = "DropPackID",
        ["InnerLv"] = "",
        ["DstFileName"] = "DropPack",
        ["OuterID"] = "DropPackID",
        ["OuterLv"] = "",
    },
    [152] = {
        ["CheckID"] = 152,
        ["SrcFileName"] = "SkillBase",
        ["InnerID"] = "FireEffect",
        ["InnerLv"] = "",
        ["DstFileName"] = "SkillLightEffect",
        ["OuterID"] = "ID",
        ["OuterLv"] = "",
    },
    [153] = {
        ["CheckID"] = 153,
        ["SrcFileName"] = "SkillBase",
        ["InnerID"] = "FlyEffect",
        ["InnerLv"] = "",
        ["DstFileName"] = "SkillLightEffect",
        ["OuterID"] = "ID",
        ["OuterLv"] = "",
    },
    [154] = {
        ["CheckID"] = 154,
        ["SrcFileName"] = "SkillBase",
        ["InnerID"] = "AreaEffect",
        ["InnerLv"] = "",
        ["DstFileName"] = "SkillLightEffect",
        ["OuterID"] = "ID",
        ["OuterLv"] = "",
    },
    [155] = {
        ["CheckID"] = 155,
        ["SrcFileName"] = "SkillBase",
        ["InnerID"] = "HitEffect",
        ["InnerLv"] = "",
        ["DstFileName"] = "SkillLightEffect",
        ["OuterID"] = "ID",
        ["OuterLv"] = "",
    },
    [156] = {
        ["CheckID"] = 156,
        ["SrcFileName"] = "SkillBase",
        ["InnerID"] = "SelfStatusID",
        ["InnerLv"] = "SelfStatusLevel",
        ["DstFileName"] = "SkillStatus",
        ["OuterID"] = "StatusID",
        ["OuterLv"] = "StatusLevel",
    },
    [157] = {
        ["CheckID"] = 157,
        ["SrcFileName"] = "SkillBase",
        ["InnerID"] = "TargetStatusID",
        ["InnerLv"] = "TargetStatusLevel",
        ["DstFileName"] = "SkillStatus",
        ["OuterID"] = "StatusID",
        ["OuterLv"] = "StatusLevel",
    },
    [158] = {
        ["CheckID"] = 158,
        ["SrcFileName"] = "SkillStatus",
        ["InnerID"] = "EffecType1",
        ["InnerLv"] = "",
        ["DstFileName"] = "SkillStatusEffect",
        ["OuterID"] = "EffecType",
        ["OuterLv"] = "EffectLevel",
    },
    [159] = {
        ["CheckID"] = 159,
        ["SrcFileName"] = "SkillStatus",
        ["InnerID"] = "EffectLevel1",
        ["InnerLv"] = "",
        ["DstFileName"] = "SkillStatusEffect",
        ["OuterID"] = "EffecType",
        ["OuterLv"] = "EffectLevel",
    },
    [160] = {
        ["CheckID"] = 160,
        ["SrcFileName"] = "SkillStatus",
        ["InnerID"] = "EffecType2",
        ["InnerLv"] = "",
        ["DstFileName"] = "SkillStatusEffect",
        ["OuterID"] = "EffecType",
        ["OuterLv"] = "EffectLevel",
    },
    [161] = {
        ["CheckID"] = 161,
        ["SrcFileName"] = "SkillStatus",
        ["InnerID"] = "EffectLevel2",
        ["InnerLv"] = "",
        ["DstFileName"] = "SkillStatusEffect",
        ["OuterID"] = "EffecType",
        ["OuterLv"] = "EffectLevel",
    },
    [162] = {
        ["CheckID"] = 162,
        ["SrcFileName"] = "SkillStatus",
        ["InnerID"] = "EffecType3",
        ["InnerLv"] = "",
        ["DstFileName"] = "SkillStatusEffect",
        ["OuterID"] = "EffecType",
        ["OuterLv"] = "EffectLevel",
    },
    [163] = {
        ["CheckID"] = 163,
        ["SrcFileName"] = "SkillStatus",
        ["InnerID"] = "EffectLevel3",
        ["InnerLv"] = "",
        ["DstFileName"] = "SkillStatusEffect",
        ["OuterID"] = "EffecType",
        ["OuterLv"] = "EffectLevel",
    },
    [164] = {
        ["CheckID"] = 164,
        ["SrcFileName"] = "SkillStatus",
        ["InnerID"] = "EffecType4",
        ["InnerLv"] = "",
        ["DstFileName"] = "SkillStatusEffect",
        ["OuterID"] = "EffecType",
        ["OuterLv"] = "EffectLevel",
    },
    [165] = {
        ["CheckID"] = 165,
        ["SrcFileName"] = "SkillStatus",
        ["InnerID"] = "EffectLevel4",
        ["InnerLv"] = "",
        ["DstFileName"] = "SkillStatusEffect",
        ["OuterID"] = "EffecType",
        ["OuterLv"] = "EffectLevel",
    },
    [166] = {
        ["CheckID"] = 166,
        ["SrcFileName"] = "SkillStatus",
        ["InnerID"] = "EffecType5",
        ["InnerLv"] = "",
        ["DstFileName"] = "SkillStatusEffect",
        ["OuterID"] = "EffecType",
        ["OuterLv"] = "EffectLevel",
    },
    [167] = {
        ["CheckID"] = 167,
        ["SrcFileName"] = "SkillStatus",
        ["InnerID"] = "EffectLevel5",
        ["InnerLv"] = "",
        ["DstFileName"] = "SkillStatusEffect",
        ["OuterID"] = "EffecType",
        ["OuterLv"] = "EffectLevel",
    },
    [168] = {
        ["CheckID"] = 168,
        ["SrcFileName"] = "PlayerExp",
        ["InnerID"] = "DropID",
        ["InnerLv"] = "",
        ["DstFileName"] = "DropPack",
        ["OuterID"] = "DropPackID",
        ["OuterLv"] = "",
    },
}

