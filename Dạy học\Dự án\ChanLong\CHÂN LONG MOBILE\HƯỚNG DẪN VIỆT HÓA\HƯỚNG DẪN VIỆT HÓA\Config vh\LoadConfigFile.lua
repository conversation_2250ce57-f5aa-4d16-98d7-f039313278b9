Lua_ConfigFile = 
{
    "Config/ActivityActivenessLvOpen",
    "Config/ActivityActivenessReward",
    "Config/ActivityAssistant",
    "Config/ActivityBase",
    "Config/ActivityBaXianIncense",
    "Config/ActivityBaXianLevel",
    "Config/ActivityBaXianNpc",
    "Config/ActivityChengJiu",
    "Config/ActivityDragonPrayEvent",
    "Config/ActivityDragonPrayLevel",
    "Config/ActivityDragonPraySkill",
    "Config/ActivityEctype",
    "Config/ActivityFarmFieldOpen",
    "Config/ActivityFarmIncense",
    "Config/ActivityFarmLevel",
    "Config/ActivityFarmPlant",
    "Config/ActivityOnline",
    "Config/ActivityOnlineActiveReward",
    "Config/ActivityOnlineDailyChargeValue",
    "Config/ActivityOnlineDailyChargeValueJR",
    "Config/ActivityOnlineDailyPackJR",
    "Config/ActivityOnlineDailyVIPPackJR",
    "Config/ActivityOnlineEveryDayCharge",
    "Config/ActivityOnlineGuoQingLogin",
    "Config/ActivityOnlineKaiFuJiJin",
    "Config/ActivityOnlineLoginReward",
    "Config/ActivityOnlineQuanMinReward",
    "Config/ActivityOnlineRechargeTimeReward",
    "Config/ActivityOnlineRechargeValueReward",
    "Config/ActivityOnlineSummonCountJR",
    "Config/ActivityOnlineSummonTotalJR",
    "Config/ActivityOnlineTimeReward",
    "Config/ActivityOnlineTotalCharge",
    "Config/ActivityOnlineTotalChargeJR",
    "Config/ActivityOnlineTotalCost",
    "Config/ActivityOnlineVIPPack",
    "Config/ActivityOnlineVIPPackJR",
    "Config/ActivityOnlineYuanBaoCritical",
    "Config/ActivityRegister",
    "Config/ActivityReward",
    "Config/ActivityScence",
    "Config/ActivityTask",
    "Config/ActivityTaskGroup",
    "Config/ActivityTaskReward",
    "Config/ActivityTurnTable",
    "Config/ActivityWorldBoss",
    "Config/ActivityWorldBossGuWu",
    "Config/ActivityWorldBossReward",
    "Config/ArenaDailyReward",
    "Config/AtkDestation",
    "Config/BattleTeach",
    "Config/CardBase",
    "Config/CardDanYaoYuanBao",
    "Config/CardDecompose",
    "Config/CardEvoluteDanYao",
    "Config/CardEvoluteProp",
    "Config/CardEvoluteSkillCondition",
    "Config/CardExp",
    "Config/CardFate",
    "Config/CardFateRelease",
    "Config/CardGroup",
    "Config/CardHunPo",
    "Config/CardIncense",
    "Config/CardRealmEctype",
    "Config/CardRealmLevel",
    "Config/CardSoul",
    "Config/ChargeIncrease",
    "Config/Check",
    "Config/Dialogue",
    "Config/DictionaryInCode",
    "Config/DropSubPackClient",
    "Config/Equip",
    "Config/EquipHeChengMaterial",
    "Config/EquipHeChengYuanBao",
    "Config/EquipPropRandType",
    "Config/EquipRefineCost",
    "Config/EquipStrengthenCost",
    "Config/EquipWorkMaterialGroup",
    "Config/FunctionOpenLevel",
    "Config/GlobalCfg",
    "Config/Guide",
    "Config/GuildActivity",
    "Config/GuildBuilding",
    "Config/GuildBuildingBankLevel",
    "Config/GuildBuildingBankReward",
    "Config/GuildBuildingSchoolLevel",
    "Config/GuildBuildingSchoolReward",
    "Config/GuildBuildingSkillAttack",
    "Config/GuildBuildingSkillAttackLevel",
    "Config/GuildBuildingSkillDefence",
    "Config/GuildBuildingSkillDefenceLevel",
    "Config/GuildBuildingSkillHp",
    "Config/GuildBuildingSkillHpLevel",
    "Config/GuildDragonPrayEvent",
    "Config/GuildDragonPrayReward",
    "Config/GuildLevel",
    "Config/GuildWorldBosssReward",
    "Config/ItemBase",
    "Config/ItemCompose",
    "Config/ItemDropGuide",
    "Config/ItemDropGuideHunPo",
    "Config/ItemDropGuideSoul",
    "Config/MailBox",
    "Config/MapBase",
    "Config/MapEctype",
    "Config/MapEctypeJingYing",
    "Config/MapEctypeSub",
    "Config/MapStarReward",
    "Config/MarketSummon",
    "Config/MonsterBase",
    "Config/MsgContent",
    "Config/OldAccounts",
    "Config/PlayerCreate",
    "Config/PlayerExp",
    "Config/PlayerTudiPosConfig",
    "Config/PlayerXianMai",
    "Config/PlayerXianMaiSkill",
    "Config/ProfessionModuls",
    "Config/QiShuSkill",
    "Config/QiShuUpgradeCost",
    "Config/QiShuZhanShu",
    "Config/QiShuZhenfa",
    "Config/SeverInfo",
    "Config/ShopPrestege",
    "Config/ShopRecharge",
    "Config/ShopSecret",
    "Config/SkillBase",
    "Config/SkillLightEffect",
    "Config/SkillStatus",
    "Config/SkillStatusEffect",
    "Config/VipLevel",
    "Config/VipLevelRight",
    "Config/ZhaoCaiShenFuPrice",
}
