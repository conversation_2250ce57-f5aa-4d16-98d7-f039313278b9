﻿

YÊU CẦU HỆ ĐIỀU HÀNH CENTOS 7.

1.Cài AA Panel:
yum install -y wget && wget -O install.sh http://www.aapanel.com/script/install_6.0_en.sh && bash install.sh

rm -f /www/server/panel/data/ssl.pl && /etc/init.d/bt restart

bt 14

Trong trang aapanel cài:
Nginx1.18
mysql5.6
php5.6
Redis [Sửa đổi cổng 6379 thành 6401 save reload và restart Redis]


2. Tắt Tường Lửa (Có Thể Không Tắt)

systemctl stop firewalld
systemctl disable firewalld

Mở Port 1:65535


3. Tải File sourcegamevn.tar Lên T<PERSON> VPS

cd /
tar zxvf sourcegamevn.tar.gz

sau đó bỏ file config.php vào đường dẫn /www/wwwroot/game/ht/user/

Cấp Quyền 777

chmod -R 777 /home
chmod -R 777 /www/wwwroot/game

4. Cài Đặt Môi Trường

ldconfig

adduser user00

chown -R user00:user00 /home/<USER>


5. Đổi pass databases： zlb

Mở mysql5.6 - tìm dòng 26 thay vào: lower_case_table_names=1
Sau đó reload -> restart lại

Sửa đổi IP cơ sở dữ liệu: ***************

/home/<USER>/dbAccount.sql
/home/<USER>/dbName_1.sql

Chạy lệnh cài sql

cd /home
./sk

Chạy xong có thể xóa file sk trong home cho an toàn

7. Tạo website

IP:81

/www/wwwroot/game


---------------------------------------------------------------------
8. Khởi động game


Chạy lệnh cấp admintrator trước
su user00

Chạy lệnh khởi động
cd /home/<USER>/Hug/Tools/Kat
./Kat.sh -S All

Sau đó chạy lệnh dừng
cd /home/<USER>/Hug/Tools/Kat
./Kat.sh -S monitor

Chạy lại lệnh khởi động lần nữa
cd /home/<USER>/Hug/Tools/Kat
./Kat.sh -S All

Kiểm tra cổng khởi động： netstat -ltnp


9. Thay ip client (***************)

APK Tìm ip ***************
\assets\LuaScripts\Login\LYP_loading.lua
\assets\LuaScripts\Login\Serverlistinfo.lua

IOS Tìm qy.xxymw.com
\Payload\xxjqt_debug_all.app\LuaScripts\Login\LYP_loading.lua
\Payload\xxjqt_debug_all.app\LuaScripts\Login\serverlistinfo.lua


GM TOOL 

GM FULL：
http://IP:81/ht/gm.php
pass： sourcegame.vn

GM USER：
http://IP:81/ht

