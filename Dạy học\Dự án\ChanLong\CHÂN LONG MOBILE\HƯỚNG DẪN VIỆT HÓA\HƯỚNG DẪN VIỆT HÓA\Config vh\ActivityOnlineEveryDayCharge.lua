ConfigMgr = ConfigMgr or {}
ConfigMgr["ActivityOnlineEveryDayCharge_"] = {
    [0] = {
        ["ID"] = 0,
        ["NeedValue"] = 0,
        ["Desc"] = "",
        ["DropClientID"] = 0,
    },
}

ConfigMgr["ActivityOnlineEveryDayCharge"] = {
    [1] = {
        ["ID"] = 1,
        ["NeedValue"] = 500,
        ["Desc"] = "Ngày đầu nạp thẻ đạt 500NB có thể nhận thưởng",
        ["DropClientID"] = 1601,
    },
    [2] = {
        ["ID"] = 2,
        ["NeedValue"] = 1000,
        ["Desc"] = "Ngày đầu nạp thẻ đạt 1000NB có thể nhận thưởng",
        ["DropClientID"] = 1602,
    },
    [3] = {
        ["ID"] = 3,
        ["NeedValue"] = 2000,
        ["Desc"] = "Ngày đầu nạp thẻ đạt 2000NB có thể nhận thưởng",
        ["DropClientID"] = 1603,
    },
    [4] = {
        ["ID"] = 4,
        ["NeedValue"] = 5000,
        ["Desc"] = "Ngày đầu nạp thẻ đạt 5000NB có thể nhận thưởng",
        ["DropClientID"] = 1604,
    },
    [5] = {
        ["ID"] = 5,
        ["NeedValue"] = 10000,
        ["Desc"] = "Ngày đầu nạp thẻ đạt 10000NB có thể nhận thưởng",
        ["DropClientID"] = 1605,
    },
    [6] = {
        ["ID"] = 6,
        ["NeedValue"] = 20000,
        ["Desc"] = "Ngày đầu nạp thẻ đạt 20000NB có thể nhận thưởng",
        ["DropClientID"] = 1606,
    },
    [7] = {
        ["ID"] = 7,
        ["NeedValue"] = 50000,
        ["Desc"] = "Ngày đầu nạp thẻ đạt 50000NB có thể nhận thưởng",
        ["DropClientID"] = 1607,
    },
}

